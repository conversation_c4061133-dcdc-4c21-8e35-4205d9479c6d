import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import '../../constants/app_constants.dart';
import '../../models/slider_model.dart';
import '../../services/image_service.dart';
import '../../services/slider_service.dart';

class SliderFormDialog extends StatefulWidget {
  final SliderModel? slider;

  const SliderFormDialog({super.key, this.slider});

  @override
  State<SliderFormDialog> createState() => _SliderFormDialogState();
}

class _SliderFormDialogState extends State<SliderFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _linkUrlController = TextEditingController();
  final _orderController = TextEditingController();
  
  String _imageUrl = '';
  bool _isActive = true;
  bool _isLoading = false;
  bool _isUploading = false;
  double _uploadProgress = 0.0;

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  Future<void> _initializeForm() async {
    if (widget.slider != null) {
      _titleController.text = widget.slider!.title;
      _descriptionController.text = widget.slider!.description ?? '';
      _linkUrlController.text = widget.slider!.linkUrl ?? '';
      _orderController.text = widget.slider!.order.toString();
      _imageUrl = widget.slider!.imageUrl;
      _isActive = widget.slider!.isActive;
    } else {
      // Get next order number for new slider
      final nextOrder = await SliderService.getNextOrder();
      _orderController.text = nextOrder.toString();
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _linkUrlController.dispose();
    _orderController.dispose();
    super.dispose();
  }

  Future<void> _pickImage() async {
    setState(() {
      _isUploading = true;
      _uploadProgress = 0.0;
    });

    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(source: ImageSource.gallery);
      
      if (pickedFile != null) {
        final uploadResult = await ImageService.uploadImage(
          pickedFile.path,
          'sliders',
          onProgress: (progress) {
            setState(() {
              _uploadProgress = progress;
            });
          },
        );
        
        if (uploadResult != null) {
          setState(() {
            _imageUrl = uploadResult;
          });
        }
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error uploading image: $e'),
          backgroundColor: AppConstants.errorColor,
        ),
      );
    } finally {
      setState(() {
        _isUploading = false;
      });
    }
  }

  void _saveSlider() {
    if (_formKey.currentState!.validate()) {
      if (_imageUrl.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please upload an image'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
        return;
      }

      final slider = SliderModel(
        id: widget.slider?.id ?? 'temp-id',
        imageUrl: _imageUrl,
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim().isNotEmpty 
            ? _descriptionController.text.trim() 
            : null,
        linkUrl: _linkUrlController.text.trim().isNotEmpty 
            ? _linkUrlController.text.trim() 
            : null,
        isActive: _isActive,
        order: int.parse(_orderController.text),
        createdAt: widget.slider?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
      );

      Navigator.of(context).pop(slider);
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.slider == null ? 'Add Slider' : 'Edit Slider'),
      content: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Image Upload
              Center(
                child: Column(
                  children: [
                    if (_isUploading) ...[
                      CircularProgressIndicator(
                        value: _uploadProgress,
                        color: AppConstants.primaryColor,
                      ),
                      const SizedBox(height: AppConstants.paddingSmall),
                      Text(
                        'Uploading: ${(_uploadProgress * 100).toStringAsFixed(0)}%',
                        style: const TextStyle(
                          fontSize: AppConstants.fontSizeSmall,
                          color: AppConstants.textSecondaryColor,
                        ),
                      ),
                    ] else if (_imageUrl.isNotEmpty) ...[
                      ClipRRect(
                        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                        child: Image.network(
                          _imageUrl,
                          height: 150,
                          width: double.infinity,
                          fit: BoxFit.cover,
                        ),
                      ),
                      const SizedBox(height: AppConstants.paddingSmall),
                      TextButton.icon(
                        onPressed: _pickImage,
                        icon: const Icon(Icons.refresh),
                        label: const Text('Change Image'),
                      ),
                    ] else ...[
                      Container(
                        height: 150,
                        width: double.infinity,
                        decoration: BoxDecoration(
                          color: AppConstants.backgroundColor,
                          borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                          border: Border.all(
                            color: AppConstants.borderColor,
                          ),
                        ),
                        child: InkWell(
                          onTap: _pickImage,
                          child: const Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.add_photo_alternate,
                                size: 50,
                                color: AppConstants.textHintColor,
                              ),
                              SizedBox(height: AppConstants.paddingSmall),
                              Text(
                                'Upload Image',
                                style: TextStyle(
                                  color: AppConstants.textSecondaryColor,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              const SizedBox(height: AppConstants.paddingMedium),
              
              // Title
              TextFormField(
                controller: _titleController,
                decoration: const InputDecoration(
                  labelText: 'Title',
                  hintText: 'Enter slider title',
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter a title';
                  }
                  return null;
                },
              ),
              const SizedBox(height: AppConstants.paddingMedium),
              
              // Description
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description (Optional)',
                  hintText: 'Enter slider description',
                ),
                maxLines: 2,
              ),
              const SizedBox(height: AppConstants.paddingMedium),
              
              // Link URL
              TextFormField(
                controller: _linkUrlController,
                decoration: const InputDecoration(
                  labelText: 'Link URL (Optional)',
                  hintText: 'Enter URL to link to',
                ),
                keyboardType: TextInputType.url,
              ),
              const SizedBox(height: AppConstants.paddingMedium),
              
              // Order
              TextFormField(
                controller: _orderController,
                decoration: const InputDecoration(
                  labelText: 'Display Order',
                  hintText: 'Enter display order (1, 2, 3...)',
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter display order';
                  }
                  if (int.tryParse(value) == null) {
                    return 'Please enter a valid number';
                  }
                  return null;
                },
              ),
              const SizedBox(height: AppConstants.paddingMedium),
              
              // Active Status
              SwitchListTile(
                title: const Text('Active'),
                subtitle: const Text('Show this slider on the app'),
                value: _isActive,
                onChanged: (value) {
                  setState(() {
                    _isActive = value;
                  });
                },
                contentPadding: EdgeInsets.zero,
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isLoading || _isUploading ? null : _saveSlider,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppConstants.primaryColor,
            foregroundColor: Colors.white,
          ),
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: Colors.white,
                  ),
                )
              : const Text('Save'),
        ),
      ],
    );
  }
}
