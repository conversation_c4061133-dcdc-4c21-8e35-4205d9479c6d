import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/slider_model.dart';

class SliderService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _collection = 'sliders';

  // Get active sliders only for user app
  static Future<List<SliderModel>> getActiveSliders() async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('isActive', isEqualTo: true)
          .orderBy('order')
          .get();

      return querySnapshot.docs
          .map((doc) => SliderModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      print('Error getting active sliders: $e');
      return [];
    }
  }

  // Get active sliders stream for real-time updates
  static Stream<List<SliderModel>> getActiveSlidersStream() {
    try {
      return _firestore
          .collection(_collection)
          .where('isActive', isEqualTo: true)
          .orderBy('order')
          .snapshots()
          .map((snapshot) => snapshot.docs
              .map((doc) => SliderModel.fromFirestore(doc))
              .toList());
    } catch (e) {
      print('Error getting active sliders stream: $e');
      return Stream.value([]);
    }
  }
}
