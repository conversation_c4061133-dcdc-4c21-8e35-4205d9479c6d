import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import '../constants/app_constants.dart';
import '../models/slider_model.dart';
import '../services/slider_service.dart';
import '../services/image_service.dart';
import '../widgets/common/loading_widget.dart';
import '../widgets/common/empty_state_widget.dart';
import '../widgets/slider_management/slider_form_dialog.dart';

class SliderManagementScreen extends StatefulWidget {
  const SliderManagementScreen({super.key});

  @override
  State<SliderManagementScreen> createState() => _SliderManagementScreenState();
}

class _SliderManagementScreenState extends State<SliderManagementScreen> {
  List<SliderModel> _sliders = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadSliders();
  }

  Future<void> _loadSliders() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final sliders = await SliderService.getAllSliders();
      if (mounted) {
        setState(() {
          _sliders = sliders;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading sliders: $e'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    }
  }

  Future<void> _addSlider() async {
    final result = await showDialog<SliderModel>(
      context: context,
      builder: (context) => const SliderFormDialog(),
    );

    if (result != null) {
      final sliderId = await SliderService.addSlider(result);
      if (sliderId != null) {
        _loadSliders();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Slider added successfully'),
              backgroundColor: AppConstants.successColor,
            ),
          );
        }
      }
    }
  }

  Future<void> _editSlider(SliderModel slider) async {
    final result = await showDialog<SliderModel>(
      context: context,
      builder: (context) => SliderFormDialog(slider: slider),
    );

    if (result != null) {
      final success = await SliderService.updateSlider(slider.id, result);
      if (success) {
        _loadSliders();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Slider updated successfully'),
              backgroundColor: AppConstants.successColor,
            ),
          );
        }
      }
    }
  }

  Future<void> _deleteSlider(SliderModel slider) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Slider'),
        content: Text('Are you sure you want to delete "${slider.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: AppConstants.errorColor),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final success = await SliderService.deleteSlider(slider.id);
      if (success) {
        _loadSliders();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Slider deleted successfully'),
              backgroundColor: AppConstants.successColor,
            ),
          );
        }
      }
    }
  }

  Future<void> _toggleSliderStatus(SliderModel slider) async {
    final success = await SliderService.toggleSliderStatus(slider.id, !slider.isActive);
    if (success) {
      _loadSliders();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        title: const Text('Slider Management'),
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _loadSliders,
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildHeader(),
          Expanded(
            child: _buildSlidersList(),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addSlider,
        backgroundColor: AppConstants.primaryColor,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: const BoxDecoration(
        color: AppConstants.surfaceColor,
        border: Border(
          bottom: BorderSide(
            color: AppConstants.borderColor,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.view_carousel,
            size: AppConstants.iconSizeLarge,
            color: AppConstants.primaryColor,
          ),
          const SizedBox(width: AppConstants.paddingMedium),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Banner Sliders',
                style: TextStyle(
                  fontSize: AppConstants.fontSizeHeading,
                  fontWeight: FontWeight.bold,
                  color: AppConstants.textPrimaryColor,
                ),
              ),
              Text(
                'Total Sliders: ${_sliders.length}',
                style: const TextStyle(
                  fontSize: AppConstants.fontSizeMedium,
                  color: AppConstants.textSecondaryColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSlidersList() {
    if (_isLoading) {
      return const LoadingWidget();
    }

    if (_sliders.isEmpty) {
      return const EmptyStateWidget(
        icon: Icons.view_carousel,
        title: 'No Sliders Found',
        subtitle: 'Add your first banner slider to get started',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      itemCount: _sliders.length,
      itemBuilder: (context, index) {
        final slider = _sliders[index];
        return _buildSliderCard(slider);
      },
    );
  }

  Widget _buildSliderCard(SliderModel slider) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Image
          ClipRRect(
            borderRadius: const BorderRadius.vertical(
              top: Radius.circular(AppConstants.borderRadiusMedium),
            ),
            child: AspectRatio(
              aspectRatio: 16 / 9,
              child: Image.network(
                slider.imageUrl,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    color: AppConstants.backgroundColor,
                    child: const Center(
                      child: Icon(
                        Icons.broken_image,
                        size: 50,
                        color: AppConstants.textHintColor,
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
          // Content
          Padding(
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        slider.title,
                        style: const TextStyle(
                          fontSize: AppConstants.fontSizeLarge,
                          fontWeight: FontWeight.bold,
                          color: AppConstants.textPrimaryColor,
                        ),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppConstants.paddingSmall,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: slider.isActive
                            ? AppConstants.successColor.withOpacity(0.1)
                            : AppConstants.errorColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                        border: Border.all(
                          color: slider.isActive
                              ? AppConstants.successColor.withOpacity(0.3)
                              : AppConstants.errorColor.withOpacity(0.3),
                        ),
                      ),
                      child: Text(
                        slider.isActive ? 'Active' : 'Inactive',
                        style: TextStyle(
                          fontSize: AppConstants.fontSizeSmall,
                          fontWeight: FontWeight.w500,
                          color: slider.isActive
                              ? AppConstants.successColor
                              : AppConstants.errorColor,
                        ),
                      ),
                    ),
                  ],
                ),
                if (slider.description != null) ...[
                  const SizedBox(height: AppConstants.paddingSmall),
                  Text(
                    slider.description!,
                    style: const TextStyle(
                      fontSize: AppConstants.fontSizeMedium,
                      color: AppConstants.textSecondaryColor,
                    ),
                  ),
                ],
                const SizedBox(height: AppConstants.paddingMedium),
                Row(
                  children: [
                    Text(
                      'Order: ${slider.order}',
                      style: const TextStyle(
                        fontSize: AppConstants.fontSizeSmall,
                        color: AppConstants.textHintColor,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: () => _toggleSliderStatus(slider),
                      icon: Icon(
                        slider.isActive ? Icons.visibility_off : Icons.visibility,
                        color: AppConstants.textSecondaryColor,
                      ),
                      tooltip: slider.isActive ? 'Deactivate' : 'Activate',
                    ),
                    IconButton(
                      onPressed: () => _editSlider(slider),
                      icon: const Icon(
                        Icons.edit,
                        color: AppConstants.primaryColor,
                      ),
                      tooltip: 'Edit',
                    ),
                    IconButton(
                      onPressed: () => _deleteSlider(slider),
                      icon: const Icon(
                        Icons.delete,
                        color: AppConstants.errorColor,
                      ),
                      tooltip: 'Delete',
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
