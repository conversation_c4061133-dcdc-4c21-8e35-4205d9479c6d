import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/slider_model.dart';

class SliderService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _collection = 'sliders';

  // Get all sliders ordered by order field
  static Future<List<SliderModel>> getAllSliders() async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .orderBy('order')
          .get();

      return querySnapshot.docs
          .map((doc) => SliderModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      print('Error getting sliders: $e');
      return [];
    }
  }

  // Get active sliders only
  static Future<List<SliderModel>> getActiveSliders() async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('isActive', isEqualTo: true)
          .orderBy('order')
          .get();

      return querySnapshot.docs
          .map((doc) => SliderModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      print('Error getting active sliders: $e');
      return [];
    }
  }

  // Add new slider
  static Future<String?> addSlider(SliderModel slider) async {
    try {
      final docRef = await _firestore.collection(_collection).add(slider.toFirestore());
      return docRef.id;
    } catch (e) {
      print('Error adding slider: $e');
      return null;
    }
  }

  // Update slider
  static Future<bool> updateSlider(String sliderId, SliderModel slider) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(sliderId)
          .update(slider.toFirestore());
      return true;
    } catch (e) {
      print('Error updating slider: $e');
      return false;
    }
  }

  // Delete slider
  static Future<bool> deleteSlider(String sliderId) async {
    try {
      await _firestore.collection(_collection).doc(sliderId).delete();
      return true;
    } catch (e) {
      print('Error deleting slider: $e');
      return false;
    }
  }

  // Update slider order
  static Future<bool> updateSliderOrder(String sliderId, int newOrder) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(sliderId)
          .update({
        'order': newOrder,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });
      return true;
    } catch (e) {
      print('Error updating slider order: $e');
      return false;
    }
  }

  // Toggle slider active status
  static Future<bool> toggleSliderStatus(String sliderId, bool isActive) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(sliderId)
          .update({
        'isActive': isActive,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });
      return true;
    } catch (e) {
      print('Error toggling slider status: $e');
      return false;
    }
  }

  // Get next order number
  static Future<int> getNextOrder() async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .orderBy('order', descending: true)
          .limit(1)
          .get();

      if (querySnapshot.docs.isEmpty) {
        return 1;
      }

      final lastOrder = querySnapshot.docs.first.data()['order'] as int? ?? 0;
      return lastOrder + 1;
    } catch (e) {
      print('Error getting next order: $e');
      return 1;
    }
  }
}
